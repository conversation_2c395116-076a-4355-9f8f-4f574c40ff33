package au.com.allianceautomation.iython.runtime;

import java.util.List;

import au.com.allianceautomation.iython.builtins.AbstractBuiltinFunction;

/**
 * A stub function that throws an error when called, indicating that the function
 * is not implemented in iython. This is used for built-in modules that are loaded
 * but not fully implemented.
 *
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class StubFunction extends AbstractBuiltinFunction {

    public StubFunction(String functionName) {
        super(functionName, 0, -1, functionName + "() -> NotImplementedError (stub)");
    }

    @Override
    protected Object execute(List<Object> args) {
        throw new RuntimeException("NotImplementedError: Function '" + getName() + "' is not implemented in iython. " +
                                 "This function requires features that are not yet supported.");
    }

    @Override
    public String toString() {
        return "<built-in function " + getName() + " (stub)>";
    }
}
